<template>
  <div class="app-container">
    <!-- 上方区域：Logo导航和用户信息合并 -->
    <div class="header">
      <div class="header-left" @click="goToHome">
        <!-- <el-button @click="toggleSidebar" type="text" class="menu-btn">
          <i class="el-icon-menu"></i>
        </el-button> -->
        <img src="../assets/imgs/logo.png" alt="Logo" class="header-logo">
        <span class="logo">CloudPivot</span>
      </div>
      <div class="header-right">
        <el-dropdown @command="handleCommand" trigger="click">
          <div class="el-dropdown-link">
            <el-avatar size="small">
              <el-icon><User style="color: #409EFF;"/></el-icon>
            </el-avatar>
            <span class="user-name">{{userName}}</span>
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人信息</el-dropdown-item>
              <el-dropdown-item command="changePassword">修改密码</el-dropdown-item>
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 下方区域：左侧菜单栏和右侧主内容区 -->
    <div class="main-container">
      <!-- 左侧菜单栏 -->
      <div class="sidebar">
        <el-menu
          :default-active="activeMenu"
          class="el-menu-vertical"
          mode="vertical"
          :collapse="sidebarCollapsed"
          @open="handleOpen"
          @close="handleClose"
        >
          <template v-for="item in menuItems" :key="item.id">
            <!-- 如果有子菜单，使用 el-sub-menu -->
            <el-sub-menu
              v-if="item.children && item.children.length > 0"
              :index="item.id.toString()"
              :disabled="!item.has_permission"
              :class="{ 'menu-item-disabled': !item.has_permission }"
            >
              <template #title>
                <el-icon>
                  <component :is="getMenuIcon(item.application_name)" />
                </el-icon>
                <span>{{ item.application_name }}</span>
              </template>

              <!-- 递归渲染子菜单 -->
              <template v-for="child in item.children" :key="child.id">
                <!-- 如果子菜单还有子菜单 -->
                <el-sub-menu
                  v-if="child.children && child.children.length > 0"
                  :index="child.id.toString()"
                  :disabled="!child.has_permission"
                  :class="{ 'menu-item-disabled': !child.has_permission }"
                >
                  <template #title>
                    <el-icon>
                      <component :is="getMenuIcon(child.application_name)" />
                    </el-icon>
                    <span>{{ child.application_name }}</span>
                  </template>

                  <!-- 第三层菜单项 -->
                  <el-menu-item
                    v-for="grandChild in child.children"
                    :key="grandChild.id"
                    :index="grandChild.id.toString()"
                    :disabled="!grandChild.has_permission"
                    :class="{ 'menu-item-disabled': !grandChild.has_permission }"
                    @click="handleMenuClick(grandChild)"
                  >
                    <el-icon>
                      <component :is="getMenuIcon(grandChild.application_name)" />
                    </el-icon>
                    <span>{{ grandChild.application_name }}</span>
                  </el-menu-item>
                </el-sub-menu>

                <!-- 如果子菜单没有子菜单，直接渲染为菜单项 -->
                <el-menu-item
                  v-else
                  :index="child.id.toString()"
                  :disabled="!child.has_permission"
                  :class="{ 'menu-item-disabled': !child.has_permission }"
                  @click="handleMenuClick(child)"
                >
                  <el-icon>
                    <component :is="getMenuIcon(child.application_name)" />
                  </el-icon>
                  <span>{{ child.application_name }}</span>
                </el-menu-item>
              </template>
            </el-sub-menu>

            <!-- 如果没有子菜单，使用普通菜单项 -->
            <el-menu-item
              v-else
              :index="item.id.toString()"
              :disabled="!item.has_permission"
              :class="{ 'menu-item-disabled': !item.has_permission }"
              @click="handleMenuClick(item)"
            >
              <el-icon>
                <component :is="getMenuIcon(item.application_name)" />
              </el-icon>
              <span>{{ item.application_name }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </div>

      <!-- 右侧主内容区 -->
      <div class="content" ref="contentRef">
        <!-- 全屏按钮（调整位置并使用绝对定位） -->
        <el-button
          @click="toggleFullScreen"
          type="text"
          class="fullscreen-btn"
        >
          <el-icon><FullScreen /></el-icon>
        </el-button>

        <!-- 新增全屏目标容器 -->
        <div class="fullscreen-target" ref="fullscreenTargetRef">
          <router-view ref="routerViewRef"></router-view>
        </div>
      </div>
    </div>
  </div>

  <!-- 修改密码对话框 -->
  <el-dialog v-model="passwordDialogVisible" width="400px" >
    <el-form :model="passwordForm" ref="passwordFormRef" :rules="passwordRules" label-width="120px" @submit.prevent="handleChangePassword">
      <el-form-item label="旧密码" prop="oldPassword" required>
        <el-input
          v-model="passwordForm.oldPassword"
          type="password"
          placeholder="请输入旧密码"
        />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword" required>
        <el-input
          v-model="passwordForm.newPassword"
          :type="newPasswordVisible ? 'text' : 'password'"
          placeholder="请输入新密码"
        >
          <template #suffix>
            <el-button
              :icon="newPasswordVisible ? View : Hide"
              @click="toggleNewPasswordVisibility"
              circle
              size="small"
            ></el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="确认新密码" prop="confirmPassword" required>
        <el-input
          v-model="passwordForm.confirmPassword"
          :type="confirmPasswordVisible ? 'text' : 'password'"
          placeholder="请输入确认新密码"
        >
          <template #suffix>
            <el-button
              :icon="confirmPasswordVisible ? View : Hide"
              @click="toggleConfirmPasswordVisibility"
              circle
              size="small"
            ></el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" native-type="submit">确定</el-button>
        <el-button @click="passwordDialogVisible = false">取消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import request from '@/utils/requests';
import {
  User, View, Hide, FullScreen, Histogram, Key, Grid, DataAnalysis, Guide, Calendar, UserFilled, Unlock,
  Management, Document, Warning, Setting, Monitor, Files, User as UserIcon, Avatar, ArrowDown
} from '@element-plus/icons-vue'; // 导入图标
import { useRouter } from 'vue-router';
const router = useRouter();

// 响应式数据
const userName = ref('');
const userRoles = ref([]); // 添加用户角色信息
const menuItems = ref([]);
const sidebarCollapsed = ref(false);
const activeMenu = ref('');

// 全屏目标容器引用
const fullscreenTargetRef = ref(null);

// 生命周期钩子
onMounted(async () => {
  await fetchUserInfo();
  await fetchMenuData();
  // router.push('/unit-management'); 
});

// 获取用户信息
const fetchUserInfo = async () => {
  const response = await request.post('/api/get_user_info.php');
  userName.value = response.user.name;
  // 假设用户信息接口返回角色ID数组，如果没有则需要单独获取
  userRoles.value = response.user.roles || [];

  // 临时设置：如果接口没有返回角色信息，可以临时设置一个测试角色
  // 请根据实际情况调整或删除这部分代码
  if (userRoles.value.length === 0) {
    // 这里设置一个测试角色ID，比如管理员角色
    userRoles.value = [1, 2]; // 假设1是系统管理员，2是应用管理员
    console.log('临时设置用户角色:', userRoles.value);
  }
};

// 获取菜单数据
const fetchMenuData = async () => {
  const response = await request.post('/api/get_user_app.php');
  console.log('菜单数据:', response); // 调试用
  console.log('用户角色:', userRoles.value); // 调试用
  // 过滤和处理菜单数据
  menuItems.value = processMenuData(response.data);
};

// 处理菜单数据
const processMenuData = (data) => {
  return data
    .filter(item => {
      // 过滤掉系统内部菜单项
      return item.application_name !== 'system';
    })
    .map(item => {
      // 处理每个菜单项
      const processedItem = {
        ...item,
        has_permission: checkPermission(item),
        children: item.children ? processMenuData(item.children) : []
      };
      return processedItem;
    })
    .sort((a, b) => a.sort_order - b.sort_order); // 按排序字段排序
};

// 检查权限 - 所有菜单均可访问
const checkPermission = (item) => {
  // 不进行权限检查，所有获取到的菜单都可以访问
  return true;
};



// 获取菜单图标
const getMenuIcon = (applicationName) => {
  const iconMap = {
    '系统管理': Setting,
    '单位管理': Histogram,
    '用户管理': UserFilled,
    '应用管理': Grid,
    '角色管理': Guide,
    '授权管理': Unlock,
    '数据大屏': DataAnalysis,
    '值班管理': Calendar,
    '重点人员管理': Management,
    '重点警情管理': Warning,
    '人员类型管理': Avatar,
    '人员信息管理': UserIcon,
    '人员状态管理': Monitor,
    '人员动向管理': Files,
    '事件信息管理': Document,
    '事件处置管理': Management
  };

  return iconMap[applicationName] || Document;
};

// 处理菜单点击事件
const handleMenuClick = (item) => {
  console.log('点击的菜单项:', item);
  if (!item.has_permission) {
    ElMessage.warning('您没有访问该功能的权限');
    return;
  }

  if (item.url) {
    console.log('点击的菜单对应的路由是:', item.url);

    // 特殊处理：schInfo 使用整个页面跳转
    if (item.url === 'schInfo') {
      console.log('schInfo 使用整个页面跳转');
      window.location.href = 'schInfo';
      return;
    }
    if (item.url === 'PerSimTS') {
  
      window.location.href = 'PerSimTS';
      return;
    }
    if (item.url === 'pcqss') {
  
      window.location.href = 'pcqss';
      return;
    }
    if (item.url === 'address_book_management.html') {
  
      window.location.href = 'address_book_management.html';
      return;
    }

    // 其他URL使用路由跳转
    router.push(item.url);
  }
};

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};

// 跳转到首页
const goToHome = () => {
  window.location.href = '/';
};

// 修改密码相关
const passwordDialogVisible = ref(false);
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});
const passwordFormRef = ref(null);
const newPasswordVisible = ref(false);
const confirmPasswordVisible = ref(false);

const toggleNewPasswordVisibility = () => {
  newPasswordVisible.value = !newPasswordVisible.value;
};

const toggleConfirmPasswordVisibility = () => {
  confirmPasswordVisible.value = !confirmPasswordVisible.value;
};

// 处理下拉菜单命令
const handleCommand = async (command) => {
  if (command === 'logout') {
    await request.get('/api/logout.php');
    window.location.href = 'login.html';
  } else if (command === 'changePassword') {
    passwordDialogVisible.value = true;
  }
};

// 处理修改密码
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return;
  await passwordFormRef.value.validate((valid) => {
    if (valid) {
      if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
        ElMessage.error('两次输入的密码不一致');
        return;
      }
      const formData = new FormData();
      formData.append('old_password', passwordForm.value.oldPassword);
      formData.append('new_password', passwordForm.value.newPassword);

      request.post('/api/change_password.php', formData).then(() => {
        ElMessage.success('密码修改成功，请重新登录');
        passwordDialogVisible.value = false;
        passwordForm.value = { oldPassword: '', newPassword: '', confirmPassword: '' };
        setTimeout(() => {
          window.location.href = 'login.html';
        }, 3000);
      }).catch(() => {
        ElMessage.error('密码修改失败');
      });
    }
  });
};

// 密码表单验证规则
const passwordRules = reactive({
  oldPassword: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
  newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
  confirmPassword: [{
    required: true,
    message: '请输入确认新密码',
    trigger: 'blur'
  }, {
    validator: (_, value, callback) => {
      if (value !== passwordForm.value.newPassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    },
    trigger: 'blur'
  }]
});

// 全屏切换
const toggleFullScreen = () => {
  const target = fullscreenTargetRef.value;
  if (!target) return;

  if (!document.fullscreenElement) {
    target.requestFullscreen().catch(err => {
      console.error('全屏失败:', err);
      ElMessage.error('全屏功能不支持');
    });
  } else {
    document.exitFullscreen();
  }
};
</script>

<style scoped>
/* 全局布局 */
.app-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  box-sizing: border-box;
}

/* 上方区域：Logo导航和用户信息合并 */
.header {
  min-height: 100px; /* 使用最小高度而不是固定高度 */
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-sizing: border-box;
  background-image: linear-gradient(to right, #bd200b, #f10d51);
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 10;
  overflow: visible; /* 确保下拉菜单可以溢出显示 */
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .header {
    padding: 0 10px;
  }
  
  .logo {
    font-size: 16px;
  }
  
  .user-name {
    max-width: 80px;
  }
}

.header-left {
  display: flex;
  align-items: center;
  flex: 0 1 auto; /* 不要扩展，允许收缩，自动基准尺寸 */
  cursor: pointer; /* 鼠标悬停时显示手型 */
  padding: 8px 12px; /* 增加点击区域 */
  border-radius: 8px; /* 圆角效果 */
  transition: background-color 0.3s ease; /* 过渡动画 */
}

.header-left:hover {
  background-color: rgba(255, 255, 255, 0.1); /* 悬停时的背景色 */
}

.header-right {
  display: flex;
  align-items: center;
  flex: 0 0 auto; /* 不扩展，不收缩，自动基准尺寸 */
  min-width: 150px; /* 确保有足够的最小宽度 */
  margin-left: auto; /* 推到最右侧 */
  height: 100%; /* 与header等高 */
  position: relative; /* 为下拉菜单提供定位上下文 */
}

/* 优化头像和用户名的样式 */
:deep(.el-avatar) {
  margin-right: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 调整下拉菜单的触发区域 */
.el-dropdown-link {
  height: 40px; /* 固定高度 */
  padding: 0 12px;
  border-radius: 20px; /* 圆角效果 */
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.el-dropdown-link:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 调整下拉菜单样式 */
:deep(.el-dropdown) {
  height: auto; /* 确保下拉菜单容器高度自适应 */
}

:deep(.el-dropdown-menu) {
  z-index: 3000; /* 确保下拉菜单显示在最上层 */
}

:deep(.el-dropdown-link) {
  white-space: nowrap; /* 防止文本换行 */
  display: inline-flex;
  align-items: center;
  gap: 8px; /* 添加间距 */
}

.menu-btn {
  color: white;
  margin-right: 15px;
}

.header-logo {
  height: 36px;
  margin-right: 10px;
}

.logo {
  font-size: 20px;
  font-weight: bold;
  color: white;
}

.user-name {
  margin: 0 10px;
  color: white;
  max-width: 120px; /* 限制用户名最大宽度 */
  overflow: hidden;
  text-overflow: ellipsis; /* 文本溢出时显示省略号 */
  white-space: nowrap; /* 防止文本换行 */
}

/* 下拉菜单样式优化 */
.el-dropdown-link {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.el-dropdown-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 确保下拉菜单正确显示 */
:deep(.el-dropdown) {
  display: inline-flex;
  align-items: center;
}

:deep(.el-dropdown-menu) {
  margin-top: 5px !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
}

:deep(.el-dropdown-menu__item) {
  padding: 8px 20px !important;
  line-height: 1.5 !important;
}

:deep(.el-icon--right) {
  margin-left: 5px;
  font-size: 12px;
  color: white;
}

/* 下方主容器：左侧菜单栏和右侧主内容区 */
.main-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧菜单栏 */
.sidebar {
  width: 200px;
  background: #34495e;
  color: #fff;
  overflow-y: auto;
  flex-shrink: 0;
  transition: width 0.3s ease;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.15);
  z-index: 5;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .sidebar {
    width: 64px;
  }
  
  .sidebar:hover {
    width: 200px;
  }
}

/* 统一菜单容器样式 */
.sidebar :deep(.el-menu) {
  background-color: #34495e !important;
  border-right: none;
}

/* 确保所有菜单文字颜色 */
.sidebar :deep(.el-menu-item),
.sidebar :deep(.el-sub-menu__title) {
  color: #bdc3c7 !important;
}

.sidebar :deep(.el-menu-item:hover),
.sidebar :deep(.el-sub-menu__title:hover) {
  background-color: #3498db !important;
}

.sidebar :deep(.el-menu-item.is-active) {
  color: #ffffff !important;
}

/* 统一所有菜单项的基础样式 */
.sidebar .el-menu-item,
.sidebar .el-sub-menu .el-menu-item,
.sidebar .el-sub-menu .el-sub-menu .el-menu-item {
  background-color: #34495e !important;
  color: #bdc3c7 !important;
  transition: all 0.3s ease;
  font-weight: 500;
  height: 45px !important;
  line-height: 45px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  margin: 2px 0;
  padding-right: 8px;
  box-sizing: border-box !important;
  min-height: 45px !important;
  max-height: 45px !important;
}

/* 统一所有菜单项的悬停和激活状态 */
.sidebar .el-menu-item:hover,
.sidebar .el-sub-menu .el-menu-item:hover,
.sidebar .el-sub-menu .el-sub-menu .el-menu-item:hover {
  background-color: #3498db !important;
  color: #ffffff !important;
}

.sidebar .el-menu-item.is-active,
.sidebar .el-sub-menu .el-menu-item.is-active,
.sidebar .el-sub-menu .el-sub-menu .el-menu-item.is-active {
  background-color: #e74c3c !important;
  color: white !important;
}


/* 统一所有禁用状态的菜单项 */
.sidebar .el-menu-item.is-disabled,
.sidebar .el-menu-item.menu-item-disabled,
.sidebar .el-sub-menu .el-menu-item.is-disabled,
.sidebar .el-sub-menu .el-menu-item.menu-item-disabled,
.sidebar .el-sub-menu .el-sub-menu .el-menu-item.is-disabled,
.sidebar .el-sub-menu .el-sub-menu .el-menu-item.menu-item-disabled {
  background-color: #34495e !important;
  color: #7f8c8d !important;
  cursor: not-allowed !important;
  opacity: 0.5;
}

.sidebar .el-menu-item.is-disabled:hover,
.sidebar .el-menu-item.menu-item-disabled:hover,
.sidebar .el-sub-menu .el-menu-item.is-disabled:hover,
.sidebar .el-sub-menu .el-menu-item.menu-item-disabled:hover,
.sidebar .el-sub-menu .el-sub-menu .el-menu-item.is-disabled:hover,
.sidebar .el-sub-menu .el-sub-menu .el-menu-item.menu-item-disabled:hover {
  background-color: #34495e !important;
  color: #7f8c8d !important;
  transform: none;
}

.sidebar .el-menu-item.is-disabled .el-icon,
.sidebar .el-menu-item.menu-item-disabled .el-icon,
.sidebar .el-sub-menu .el-menu-item.is-disabled .el-icon,
.sidebar .el-sub-menu .el-menu-item.menu-item-disabled .el-icon,
.sidebar .el-sub-menu .el-sub-menu .el-menu-item.is-disabled .el-icon,
.sidebar .el-sub-menu .el-sub-menu .el-menu-item.menu-item-disabled .el-icon {
  color: #7f8c8d !important;
}

/* 子菜单标题样式 - 与菜单项保持视觉一致性 */
.sidebar .el-sub-menu__title,
.sidebar .el-sub-menu .el-sub-menu__title,
.sidebar .el-sub-menu .el-sub-menu .el-sub-menu__title {
  background-color: #34495e !important;
  color: #ecf0f1 !important;
  font-weight: 500;
  height: 45px !important;
  line-height: 45px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin: 2px 0;
  transition: all 0.3s ease;
  box-sizing: border-box !important;
  min-height: 45px !important;
  max-height: 45px !important;
}

.sidebar .el-sub-menu .el-sub-menu__title:hover,
.sidebar .el-sub-menu .el-sub-menu .el-sub-menu__title:hover {
  background-color: #3498db !important;
  color: #ffffff !important;
  transform: translateX(2px);
}

/* 优化菜单图标样式 */
.sidebar .el-icon {
  margin-right: 10px;
  font-size: 16px;
  color: inherit;
  vertical-align: middle;
}

/* 确保子菜单展开时的过渡效果 */
.sidebar .el-sub-menu__title,
.sidebar .el-menu-item {
  transition: all 0.3s ease !important;
}

/* 子菜单展开时的样式 */
.sidebar .el-menu--inline {
  background-color: #2c3e50 !important;
  padding: 0;
}

/* 调整不同级别菜单的缩进 */
.sidebar .el-menu-item {
  padding-left: 20px !important;
}

.sidebar .el-sub-menu .el-menu-item {
  padding-left: 40px !important;
}

.sidebar .el-sub-menu .el-sub-menu .el-menu-item {
  padding-left: 60px !important;
}

/* 折叠状态下的菜单样式 */
.sidebar .el-menu--collapse {
  width: 64px;
}

.sidebar .el-menu--collapse .el-sub-menu__title span,
.sidebar .el-menu--collapse .el-menu-item span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}

.sidebar .el-menu--collapse .el-sub-menu__icon-arrow {
  display: none;
}

/* 折叠状态下的悬停提示 */
.sidebar .el-menu--collapse .el-tooltip {
  padding: 0 !important;
}

/* 菜单项的阴影和过渡效果 */
.sidebar .el-menu-item.is-active {
  box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
  position: relative;
}

.sidebar .el-menu-item.is-active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #e74c3c;
}

/* 优化菜单项的图标和文本对齐 */
.sidebar .el-menu-item,
.sidebar .el-sub-menu__title {
  display: flex;
  align-items: center;
}

.sidebar .el-menu-item span,
.sidebar .el-sub-menu__title span {
  margin-left: 5px;
}

/* 强制所有菜单项高度一致 */
.sidebar :deep(.el-menu-item),
.sidebar :deep(.el-sub-menu__title) {
  height: 45px !important;
  line-height: 45px !important;
  min-height: 45px !important;
  max-height: 45px !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
}

/* 右侧主内容区 */
.content {
  flex: 1;
  position: relative; /* 为按钮定位提供参考 */
  padding: 10px;
  background: #f0f2f5;
  overflow-y: auto;
}

/* 全屏目标容器 */
.fullscreen-target {
  position: relative;
  width: 100%;
  height: 100%; /* 顶部70px + 间距10px */
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: white;
}

/* 全屏按钮样式 */
.fullscreen-btn {
  position: absolute;
  right: 20px;
  bottom: 20px;
  z-index: 1000; /* 确保按钮在最上层 */
  background: rgba(255, 255, 255, 1); /* 半透明背景 */
  border-radius: 10px;
  padding: 8px;
}

/* .fullscreen-btn:hover {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
} */

/* 全屏状态下隐藏按钮 */
:fullscreen .fullscreen-btn {
  display: none;
}

/* 表单样式优化 */
.el-form-item {
  margin-bottom: 20px;
  margin-top: 10px;
}

.el-input__suffix {
  padding-right: 10px;
}

.el-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>